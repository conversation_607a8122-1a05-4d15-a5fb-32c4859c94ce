import os
import pandas as pd
from pathlib import Path
import openpyxl
from openpyxl.utils.dataframe import dataframe_to_rows
import re

def find_history_files(base_folder):
    """Miyake_Exportフォルダ以降で02_History_*.xlsxファイルを検索"""
    history_files = []
    base_path = Path(base_folder)
    
    for file_path in base_path.rglob("02_History_*.xlsx"):
        history_files.append(file_path)
    
    return sorted(history_files)

def clean_sheet_name(file_stem):
    """ファイル名から余計な文字列を削除してシート名を作成"""
    # アンダースコアで分割
    parts = file_stem.split('_')
    
    # 最初の4つの部分を取得
    cleaned_parts = parts[:4]
    
    # 4つ目の部分に日本語や記号が含まれている場合は除外
    if len(cleaned_parts) == 4:
        fourth_part = cleaned_parts[3]
        # 英数字と括弧、スペースのみかチェック
        if not re.match(r'^[a-zA-Z0-9()\s]+$', fourth_part):
            cleaned_parts = cleaned_parts[:3]
    
    cleaned_name = '_'.join(cleaned_parts)
    return cleaned_name

def merge_accumulationTime_sheets(base_folder, output_file="merged_accumulationtime.xlsx"):
    """AccumulationTimeシートをまとめて新しいエクセルファイルに統合"""
    history_files = find_history_files(base_folder)
    
    if not history_files:
        print("02_History_*.xlsxファイルが見つかりませんでした。")
        return
    
    # 新しいワークブックを作成
    output_wb = openpyxl.Workbook()
    output_wb.remove(output_wb.active)  # デフォルトシートを削除
    
    for file_path in history_files:
        try:
            # エクセルファイルを開く
            wb = openpyxl.load_workbook(file_path, data_only=True)
            
            if "AccumulationTime" not in wb.sheetnames:
                print(f"警告: {file_path.name} に 'AccumulationTime' シートが見つかりません。")
                continue
            
            # Mileageシートを取得
            mileage_sheet = wb["AccumulationTime"]
            
            # 新しいシート名（余計な文字列を削除）
            sheet_name = clean_sheet_name(file_path.stem)
            
            # 新しいワークブックにシートを作成
            new_sheet = output_wb.create_sheet(title=sheet_name)
            
            # データをコピー
            for row in mileage_sheet.iter_rows(values_only=True):
                new_sheet.append(row)
            
            print(f"コピー完了: {file_path.name} -> {sheet_name}")
            
        except Exception as e:
            print(f"エラー: {file_path.name} の処理中にエラーが発生しました: {e}")
    
    # スクリプト実行フォルダ内に結果を保存
    current_dir = Path.cwd()
    output_path = current_dir / output_file
    output_wb.save(output_path)
    print(f"統合完了: {output_path}")

if __name__ == "__main__":
    # 01_Miyake_Exportフォルダのパスを指定
    base_folder = "01_Miyake_Export"  # 適切なパスに変更してください
    
    # 統合実行
    merge_accumulationTime_sheets(base_folder)









