# 🚀 DTC UWB Analysis Tools - クイックスタートガイド
## 📋 5分で始める車両故障データ分析

### 🎯 このガイドの目的
**初めての方でも5分で分析を開始**できるように、最低限必要な手順をまとめました。

---

## ⚡ 超簡単3ステップ

### ステップ1️⃣: ファイルを準備 (1分)
1. **分析したいExcelファイル**を `01_Miyake_Export/` フォルダに入れる
2. ファイル名が `02_History_XXXX.xlsx` の形式になっているか確認

### ステップ2️⃣: 環境確認 (2分)
1. **コマンドプロンプトを開く** (Windowsキー + R → `cmd`)
2. **Pythonの確認**
   ```bash
   python --version
   ```
3. **ライブラリのインストール**
   ```bash
   pip install pandas openpyxl --proxy http://proxy.mei.co.jp:8080
   ```

### ステップ3️⃣: 分析実行 (2分)
1. **フォルダに移動**
   ```bash
   cd "パス\E-cap_DTC_UWB_Analysis"
   ```
2. **分析実行**
   ```bash
   python 01_Merge_AccumulationTime_sheets.py
   python 02_DTC_Code_analysis.py
   ```

---

## 🎉 完了！結果の確認

### 📊 作成されるファイル
- `merged_accumulationtime.xlsx` - 統合されたデータ
- `02_DTC_Code_Analysis_Result/DTC_ranking.csv` - 故障ランキング

### 📈 結果の見方
1. **DTC_ranking.csv** を開く
2. **一番上の行** = 最も多い故障
3. **数字が大きい** = より多くの車両で発生

---

## 🚨 困ったときの対処法

| 問題 | 解決方法 |
|------|----------|
| Pythonエラー | Python公式サイトからインストール |
| ファイルが見つからない | ファイル名とフォルダを確認 |
| その他のエラー | README.mdの詳細ガイドを参照 |

---

## 📚 詳細情報
- **詳しい使い方**: `README.md` を参照
- **PDF版**: VS Codeで `README.md` を開いて PDF出力
- **トラブル解決**: README.mdの「🚨 トラブルシューティング」を参照

---

**🎯 これで基本的な分析ができます！詳細な機能は README.md をご覧ください。**
