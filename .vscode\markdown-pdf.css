/* PDF出力用のカスタムスタイル */

body {
    font-family: 'Segoe UI', 'Yu Gothic', 'Meiryo', sans-serif;
    font-size: 12px;
    line-height: 1.6;
    color: #333;
    max-width: none;
    margin: 0;
    padding: 20px;
}

/* ヘッダースタイル */
h1 {
    color: #2c3e50;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    margin-top: 30px;
    margin-bottom: 20px;
    font-size: 24px;
    page-break-after: avoid;
}

h2 {
    color: #34495e;
    border-bottom: 2px solid #95a5a6;
    padding-bottom: 5px;
    margin-top: 25px;
    margin-bottom: 15px;
    font-size: 18px;
    page-break-after: avoid;
}

h3 {
    color: #2c3e50;
    margin-top: 20px;
    margin-bottom: 10px;
    font-size: 16px;
    page-break-after: avoid;
}

/* コードブロックスタイル */
pre {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 15px;
    margin: 15px 0;
    overflow-x: auto;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 11px;
    line-height: 1.4;
    page-break-inside: avoid;
}

code {
    background-color: #f1f3f4;
    padding: 2px 4px;
    border-radius: 3px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 11px;
}

/* リストスタイル */
ul, ol {
    margin: 10px 0;
    padding-left: 25px;
}

li {
    margin: 5px 0;
}

/* テーブルスタイル */
table {
    border-collapse: collapse;
    width: 100%;
    margin: 15px 0;
    page-break-inside: avoid;
}

th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

/* ページ区切り設定 */
.page-break {
    page-break-before: always;
}

/* 強調表示 */
strong {
    color: #2c3e50;
    font-weight: bold;
}

em {
    color: #7f8c8d;
    font-style: italic;
}

/* 引用スタイル */
blockquote {
    border-left: 4px solid #3498db;
    margin: 15px 0;
    padding: 10px 20px;
    background-color: #f8f9fa;
    font-style: italic;
}

/* リンクスタイル */
a {
    color: #3498db;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

/* ディレクトリ構造の表示改善 */
pre code {
    background-color: transparent;
    padding: 0;
}

/* 日本語フォントの改善 */
@media print {
    body {
        font-family: 'Yu Gothic', 'Meiryo', 'MS Gothic', sans-serif;
    }
    
    /* ページ区切りの制御 */
    h1, h2, h3 {
        page-break-after: avoid;
    }
    
    pre, table {
        page-break-inside: avoid;
    }
}
