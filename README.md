# E-cap DTC UWB Analysis Tools

このリポジトリには、DTC（故障コード）とUWB（車両情報）データの分析に使用される3つの主要なスクリプトファイルが含まれています。

## 1. 01_Merge_AccumulationTime_sheets.py

### 機能
このスクリプトは、複数のExcelファイルからAccumulationTimeシートを抽出し、1つの新しいExcelファイルに統合するスクリプトファイルです。

### 使用方法
```bash
python 01_Merge_AccumulationTime_sheets.py
```

### 出力
- `merged_accumulationtime.xlsx`: 統合されたExcelファイル
  - 各元ファイルのAccumulationTimeシートが個別のシートとして保存されます
  - シート名はファイル名から適切にクリーニングして作成されます

## 2. 02_DTC_Code_analysis.py

### 機能
このスクリプトは、統合されたDTCデータから以下の分析を行います：

1. **DTCコードの頻度分析**
   - DTCコードの出現頻度をランキング化
   - シートごとのDTCコードの存在マトリクスを作成
   - 重複を除去したユニークなDTCデータを抽出

2. **データ出力**
   - `output`フォルダに以下のファイルを生成
     - `DTC_ranking.csv`: DTCコードの頻度ランキング
     - `DTC_matrix.csv`: 通常のDTCマトリクス
     - `DTC_matrix_transposed.csv`: 横向きのDTCマトリクス
     - `all_extracted_DTC_Code.csv`: 重複除去済みの全DTCデータ
   - `merged_accumulationtime.xlsx`に以下のシートを追加
     - `DTC_Frequency`: DTCコードの頻度データ
     - `DTC_Matrix`: シートごとのDTC存在マトリクス
     - `DTC_matrix_transposed`: 横向きのDTCマトリクス

### 使用方法
```bash
python 02_DTC_Code_analysis.py
```

### 依存関係
- `merged_accumulationtime.xlsx`: 01_Merge_AccumulationTime_sheets.pyの出力が必要

## 3. 03_Create_DTC_Timeline_Summary.py

### 機能
このスクリプトは、DTCとUWBデータの時系列分析を行います：

1. **データ読み込み**
   - `03_Input_Files/UWB_Extraction_List.csv`: 抽出対象のUWBコード情報
   - `03_Input_Files/DTC_Extraction_List.csv`: 抽出対象のDTCコード情報
   - `merged_accumulationtime.xlsx`: 統合されたDTCデータ

2. **データ処理**
   - DTCコードと説明の抽出
   - UWBコードと説明の抽出
   - 数値データの単位除去
   - ターゲットDTCコードに基づいたデータ抽出

3. **時系列サマリ作成**
   - DTCコード、マイルド、累積時間、UWBコードなどの時系列データを統合
   - エラー処理とデータの整合性チェック

### 使用方法
```bash
python 03_Create_DTC_Timeline_Summary.py
```

### 依存関係
- `03_Input_Files`フォルダ内のCSVファイル
- `merged_accumulationtime.xlsx`: 01_Merge_AccumulationTime_sheets.pyの出力

## 共通の前提条件

```bash
pip install pandas openpyxl  --proxy http://proxy.mei.co.jp:8080
```

## ディレクトリ構造

```
E-cap_DTC_UWB_Analysis/
├── 01_Miyake_Export/           # 01_Miyake_Export以下の階層であれば分析対象とする / 元のExcelファイル 02_History_XXX.xlsxであれば分析対象とする
│   ├── 追加1台(250714)
│   |   └── 02_History_2426000027.xlsx
│   ├── 02_History_1456_24B33020292.xlsx
│   :
│   :
│   └── 02_History_DTC_2429510138.xlsx
├── 02_DTC_Code_Analysis_Result/                     # DTCコード分析結果
│   ├── DTC_ranking.csv
│   ├── DTC_matrix.csv
│   ├── DTC_matrix_transposed.csv
│   └── all_extracted_DTC_Code.csv
├── 03_Input_Files/             # 入力CSVファイル
│   ├── UWB_Extraction_List.csv # 抽出対象のUWBコード情報　解析したい事象に応じて抽出対象を更新
│   └── DTC_Extraction_List.csv # 抽出対象のDTCコード情報　解析したい事象に応じて抽出対象を更新
├── 01_Merge_AccumulationTime_sheets.py
├── 02_DTC_Code_analysis.py
├── 03_Create_DTC_Timeline_Summary.py
└── README.md
```

## 使用フロー

1. `01_Merge_AccumulationTime_sheets.py`を実行してExcelファイルを統合
2. `02_DTC_Code_analysis.py`を実行してDTCコードの分析を行う
3. `03_Create_DTC_Timeline_Summary.py`を実行してサマリを作成

## エラー処理

各スクリプトは以下のエラー状況に対応しています：
- ファイルが見つからない場合
- 必要なシートが存在しない場合
- データ形式が不正な場合
- その他の予期せぬエラー

各エラー発生時に詳細なメッセージが表示されます。

## 注意事項

- 出力ファイルは上書きされますので、必要な場合には事前にバックアップを取ってください
- ファイル名の規則に従って処理されますので、ファイル名の形式に注意してください
- エラーが発生したファイルはスキップされますが、処理は継続されます

## ライセンス
