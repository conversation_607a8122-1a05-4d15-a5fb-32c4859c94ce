---
marp: true
theme: default
header: "E-cap DTC UWB Analysis Tools"
footer: "20250821 Ta<PERSON>hashi"
---
# E-cap DTC UWB Analysis Tools
## 🚗 車両故障診断データ分析ツール
---
### 📋 このツールで何ができるの？
このツールは、**車両の故障診断データを簡単に分析できる**ツールです。
- **DTC（故障コード）**: 車両に発生した故障の種類を示すコード
- **UWB（車両情報）**: 車両の状態や動作に関する情報
---
### 🎯 誰のためのツール？

- 車両の故障データを分析したい方
- Excelファイルがたくさんあって整理に困っている方
- 故障の傾向やパターンを知りたい方
- **プログラミング初心者でも大丈夫！**
---
### ✨ このツールの3つの機能

| 機能 | 何をするツール？ | 結果 |
|------|------------------|------|
| **①データ統合** | バラバラのExcelファイルを1つにまとめる | 📊 1つの大きなExcelファイル |
| **②故障分析** | どの故障が多いかランキングを作る | 📈 故障の頻度ランキング |
| **③時系列分析** | 故障がいつ起きたかを時間順に整理 | 📅 時系列レポート |

---

## 🚀 使い方ガイド（初心者向け）

### 📁 事前準備

#### ステップ1: フォルダの確認
以下のフォルダ構造になっているか確認してください：

```
E-cap_DTC_UWB_Analysis/
├── 01_Miyake_Export/           ← ここに分析したいExcelファイルを入れる
│   ├── 02_History_XXXX.xlsx    ← このような名前のファイル
│   ├── 02_History_YYYY.xlsx
│   └── ...
├── 03_Input_Files/             ← 設定ファイルが入っている
└── (Pythonスクリプトファイル群)
```
---

#### ステップ2: 必要なソフトウェアの確認
- **Python** がインストールされているか確認
- **コマンドプロンプト** または **PowerShell** が使えるか確認

---

## 📊 機能1: データ統合ツール(`01_Merge_AccumulationTime_sheets.py`)
#### 🤔 何をするツール？
- バラバラのExcelファイルを **1つのファイルにまとめる**
- 各ファイルの「AccumulationTime」シートを抽出
---
## 📊 機能1: データ統合ツール(`01_Merge_AccumulationTime_sheets.py`)
#### 📝 使い方
1. **コマンドプロンプトを開く**
   - Windowsキー + R → `cmd` と入力 → Enter
2. **フォルダに移動**
   ```bash
   cd "C:\Users\<USER>\Downloads\E-cap_DTC_UWB_Analysis\E-cap_DTC_UWB_Analysis"
   ```
3. **スクリプトを実行**
   ```bash
   python 01_Merge_AccumulationTime_sheets.py
   ```
---
## 📊 機能1: データ統合ツール(`01_Merge_AccumulationTime_sheets.py`)

#### 📤 何ができる？
- **入力**: `01_Miyake_Export/`フォルダ内の複数のExcelファイル
- **出力**: `merged_accumulationtime.xlsx` （1つの統合ファイル）

#### ✅ 成功の確認方法
- 実行後、フォルダに `merged_accumulationtime.xlsx` ができていればOK
- ファイルを開いて、複数のシートがあることを確認

---

## 📈 機能2: 故障分析ツール(`02_DTC_Code_analysis.py`)

#### 🤔 何をするツール？
- **どの故障が一番多いか**をランキング形式で表示
- **どの車両にどの故障が起きているか**を一覧表で表示
- 同じ故障の重複を除去してスッキリ整理
---

## 📈 機能2: 故障分析ツール(`02_DTC_Code_analysis.py`)
#### 📝 使い方
1. **前提条件**: 機能1を先に実行して `merged_accumulationtime.xlsx` を作成
2. **コマンドプロンプトで実行**
   ```bash
   python 02_DTC_Code_analysis.py
   ```
---
## 📈 機能2: 故障分析ツール(`02_DTC_Code_analysis.py`)
#### 📤 何ができる？
##### 📊 作成されるファイル一覧
| ファイル名 | 内容 | 見方 |
|-----------|------|------|
| `DTC_ranking.csv` | 故障コードの頻度ランキング | 一番上が最も多い故障 |
| `DTC_matrix.csv` | 車両×故障コードの一覧表 | ○があれば該当車両で発生 |
| `DTC_matrix_transposed.csv` | 横向きの一覧表 | 上記の横向きバージョン |
| `all_extracted_DTC_Code.csv` | 全故障データ（重複なし） | 全ての故障情報 |
---
## 📈 機能2: 故障分析ツール(`02_DTC_Code_analysis.py`)
##### 📁 保存場所
- **CSVファイル**: `02_DTC_Code_Analysis_Result/` フォルダ
- **Excelシート**: `merged_accumulationtime.xlsx` に追加

#### 💡 結果の見方
1. **`DTC_ranking.csv`** を開く
2. 一番上の行が **最も頻繁に起きている故障**
3. 数字が大きいほど多くの車両で発生している故障
---
## 📈 機能2: 故障分析ツール(`02_DTC_Code_analysis.py`)
#### ✅ 成功の確認方法
- `02_DTC_Code_Analysis_Result/` フォルダができている
- 中に4つのCSVファイルがある
- `merged_accumulationtime.xlsx` に新しいシートが追加されている

---

## 📅 機能3: 時系列分析ツール(`03_Create_DTC_Timeline_Summary.py`)

#### 🤔 何をするツール？
- **故障がいつ起きたか**を時間順に整理
- **特定の故障だけ**を抽出して詳細分析
- 車両情報（UWB）と故障情報（DTC）を組み合わせて表示
---

## 📅 機能3: 時系列分析ツール(`03_Create_DTC_Timeline_Summary.py`)
#### 📝 事前設定（重要！）

##### ステップ1: 分析したい故障を指定
1. `03_Input_Files/DTC_Extraction_List.csv` を開く
2. 分析したい故障コードを入力
   ```csv
   DTC_Code,Description
   P0001,燃料システム異常
   P0002,エンジン制御異常
   ```
---

## 📅 機能3: 時系列分析ツール(`03_Create_DTC_Timeline_Summary.py`)
##### ステップ2: 分析したい車両情報を指定
1. `03_Input_Files/UWB_Extraction_List.csv` を開く
2. 分析したい車両情報コードを入力
   ```csv
   UWB_Code,Description
   U001,エンジン回転数
   U002,車速
   ```
---

## 📅 機能3: 時系列分析ツール(`03_Create_DTC_Timeline_Summary.py`)
#### 📝 使い方
1. **前提条件**: 機能1と機能2を先に実行
2. **設定ファイルを編集**（上記の事前設定）
3. **コマンドプロンプトで実行**
   ```bash
   python 03_Create_DTC_Timeline_Summary.py
   ```
---

## 📅 機能3: 時系列分析ツール(`03_Create_DTC_Timeline_Summary.py`)
#### 📤 何ができる？
- **入力**:
  - 統合されたExcelファイル
  - 抽出設定ファイル（CSV）
- **出力**: 時系列サマリレポート
---

## 📅 機能3: 時系列分析ツール(`03_Create_DTC_Timeline_Summary.py`)
#### 💡 結果の見方
- 指定した故障コードが **いつ、どの車両で** 発生したかが時間順に表示
- 車両の状態情報も同時に確認可能

#### ✅ 成功の確認方法
- エラーメッセージが出ずに完了
- 出力ファイルが作成されている

---

## 🔧 初期設定（最初に1回だけ）

### ステップ1: Pythonの確認
1. **コマンドプロンプトを開く**
2. **以下を入力してEnter**
   ```bash
   python --version
   ```
3. **結果の確認**
   - `Python 3.7.x` 以上が表示されればOK
   - エラーが出る場合は、Pythonをインストールしてください
---
### ステップ2: 必要なライブラリのインストール
1. **コマンドプロンプトで以下を実行**
   ```bash
   pip install pandas openpyxl --proxy http://proxy.mei.co.jp:8080
   ```
2. **成功メッセージが表示されればOK**

### ステップ3: 動作環境の確認

| 項目 | 推奨環境 | 確認方法 |
|------|----------|----------|
| **Python** | 3.7以上 | `python --version` |
| **OS** | Windows 10/11 | システム情報で確認 |
| **メモリ** | 4GB以上 | タスクマネージャーで確認 |
| **ディスク容量** | 1GB以上の空き | エクスプローラーで確認 |

---

## 📁 フォルダ構造の説明

### 🗂️ 基本構造
```
E-cap_DTC_UWB_Analysis/
├── 📂 01_Miyake_Export/              ← 【重要】ここに分析したいExcelファイルを入れる
│   ├── 📂 追加1台(250714)/          ← サブフォルダがあってもOK
│   │   └── 📄 02_History_2426000027.xlsx
│   ├── 📄 02_History_1456_24B33020292.xlsx  ← このような名前のファイル
│   └── 📄 02_History_DTC_2429510138.xlsx
├── 📂 02_DTC_Code_Analysis_Result/   ← 分析結果が自動で保存される
│   ├── 📄 DTC_ranking.csv           ← 故障ランキング
│   ├── 📄 DTC_matrix.csv            ← 故障マトリクス
│   ├── 📄 DTC_matrix_transposed.csv ← 横向きマトリクス
│   └── 📄 all_extracted_DTC_Code.csv ← 全故障データ
├── 📂 03_Input_Files/               ← 設定ファイル（編集可能）
│   ├── 📄 UWB_Extraction_List.csv   ← 車両情報の抽出設定
│   └── 📄 DTC_Extraction_List.csv   ← 故障コードの抽出設定
├── 🐍 01_Merge_AccumulationTime_sheets.py  ← 実行ファイル1
├── 🐍 02_DTC_Code_analysis.py              ← 実行ファイル2
├── 🐍 03_Create_DTC_Timeline_Summary.py    ← 実行ファイル3
└── 📖 README.md                            ← このファイル
```
---
### 📋 各フォルダの役割

| フォルダ名 | 役割 | あなたがすること |
|-----------|------|------------------|
| `01_Miyake_Export/` | 分析対象のExcelファイル置き場 | **Excelファイルをここに入れる** |
| `02_DTC_Code_Analysis_Result/` | 分析結果の保存場所 | 結果ファイルを確認する |
| `03_Input_Files/` | 分析設定ファイル | 必要に応じて設定を変更 |

### ⚠️ 重要なファイル名のルール
- 分析対象のExcelファイルは **`02_History_`** で始まる名前にしてください
- 例: `02_History_車両番号.xlsx`

---

## 🚀 実際の使い方（ステップバイステップ）

### 📋 全体の流れ
```
📄 複数のExcelファイル → 🔄 統合 → 📊 分析 → 📅 時系列レポート
```

### ステップ1️⃣: データ統合
```bash
python 01_Merge_AccumulationTime_sheets.py
```
**結果**: `merged_accumulationtime.xlsx` ができる

### ステップ2️⃣: 故障分析
```bash
python 02_DTC_Code_analysis.py
```
**結果**: 故障ランキングとマトリクスができる

### ステップ3️⃣: 時系列分析（オプション）
```bash
python 03_Create_DTC_Timeline_Summary.py
```
**結果**: 時系列レポートができる

### 💡 実行のコツ
- **必ず順番通り**に実行してください
- 各ステップが完了してから次に進む
- エラーが出たら、そのステップをもう一度実行

---

## 🚨 トラブルシューティング

### よくあるエラーと解決方法

#### ❌ エラー1: 「ファイルが見つかりません」
**原因**: 必要なファイルがない
**解決方法**:
1. `01_Miyake_Export/` フォルダに Excelファイルがあるか確認
2. ファイル名が `02_History_` で始まっているか確認

#### ❌ エラー2: 「Pythonが認識されません」
**原因**: Pythonがインストールされていない
**解決方法**:
1. Python公式サイトからダウンロード
2. インストール時に「Add to PATH」にチェック

#### ❌ エラー3: 「モジュールが見つかりません」
**原因**: 必要なライブラリがインストールされていない
**解決方法**:
```bash
pip install pandas openpyxl --proxy http://proxy.mei.co.jp:8080
```

#### ❌ エラー4: 「シートが存在しません」
**原因**: Excelファイルに「AccumulationTime」シートがない
**解決方法**:
1. Excelファイルを開いて確認
2. 正しいファイルかどうか確認

### 🔍 エラーメッセージの見方
- **赤い文字**: エラーメッセージ
- **ファイル名**: 問題のあるファイル
- **行番号**: エラーが起きた場所

### 📞 困ったときは
1. **エラーメッセージをコピー**
2. **どのステップで起きたか**を記録
3. **使用したファイル名**を記録
4. 開発チームに相談

---

## ⚠️ 重要な注意事項

### 💾 データのバックアップ
- **出力ファイルは上書きされます**
- 重要なデータは事前にバックアップを取ってください

### 📝 ファイル名のルール
- 分析対象: `02_History_XXXX.xlsx`
- この形式以外のファイルは無視されます

### 🔄 処理の継続
- 1つのファイルでエラーが起きても、他のファイルの処理は継続されます
- 最後にエラーの一覧が表示されます

---

## 📚 よくある質問（FAQ）

### Q1: プログラミングの知識がなくても使えますか？
**A**: はい！このガイドに従って、コマンドをコピー＆ペーストするだけで使えます。

### Q2: どのくらいの時間がかかりますか？
**A**:
- データ統合: 数分〜10分程度
- 故障分析: 1〜5分程度
- 時系列分析: 1〜3分程度

### Q3: 大量のファイルでも大丈夫ですか？
**A**: はい。ただし、ファイル数が多い場合は時間がかかります。

### Q4: 結果はどこに保存されますか？
**A**:
- CSVファイル: `02_DTC_Code_Analysis_Result/` フォルダ
- Excelファイル: 元のフォルダに `merged_accumulationtime.xlsx`

### Q5: エラーが出たらどうすればいいですか？
**A**: 上記の「🚨 トラブルシューティング」を参照してください。

---

## 📖 用語集

| 用語 | 意味 |
|------|------|
| **DTC** | 故障診断コード（Diagnostic Trouble Code） |
| **UWB** | 車両情報データ |
| **AccumulationTime** | 累積時間データが記録されたExcelシート |
| **CSV** | カンマ区切りのデータファイル |
| **マトリクス** | 行と列で整理された表形式のデータ |

---

## 📄 ライセンス

本ソフトウェアは社内利用を目的として開発されています。

---

## 📅 更新履歴

| バージョン | 日付 | 更新内容 |
|-----------|------|----------|
| v1.0 | 2025-08-21 | 初版リリース |
| v1.1 | 2025-08-21 | 初心者向けガイド追加 |

---

## 🎯 まとめ

このツールを使えば、**複雑な車両故障データの分析が簡単**にできます！

### 🚀 3ステップで完了
1. **データ統合** → バラバラのファイルを1つに
2. **故障分析** → どの故障が多いかランキング
3. **時系列分析** → いつ故障が起きたか確認

### 💪 このツールの強み
- **プログラミング不要** - コマンドをコピペするだけ
- **自動化** - 手作業でのデータ整理が不要
- **視覚的** - 結果がわかりやすい表形式で出力

**さあ、始めてみましょう！** 🚗✨

