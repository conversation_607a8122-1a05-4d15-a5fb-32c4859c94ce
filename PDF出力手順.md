# README.md をPDFで出力する手順

## 前提条件

1. **VS Code** がインストールされていること
2. **Markdown PDF** 拡張機能がインストールされていること

## Markdown PDF 拡張機能のインストール

1. VS Codeを開く
2. 拡張機能タブ（Ctrl+Shift+X）を開く
3. 検索ボックスに「Markdown PDF」と入力
4. 「Markdown PDF」（作者: yzane）をインストール

## PDF出力手順

### 方法1: コマンドパレットから実行

1. VS CodeでREADME.mdファイルを開く
2. `Ctrl+Shift+P` でコマンドパレットを開く
3. 「Markdown PDF: Export (pdf)」と入力して選択
4. PDFファイルが同じディレクトリに生成される

### 方法2: 右クリックメニューから実行

1. VS CodeでREADME.mdファイルを開く
2. エディタ内で右クリック
3. 「Markdown PDF: Export (pdf)」を選択
4. PDFファイルが同じディレクトリに生成される

## 出力設定

プロジェクトには以下の設定ファイルが含まれています：

- `.vscode/settings.json`: PDF出力の基本設定
- `.vscode/markdown-pdf.css`: PDF用のスタイル設定

### 主な設定内容

- **用紙サイズ**: A4
- **向き**: 縦向き
- **余白**: 上1.5cm、その他1cm
- **フォント**: 日本語対応フォント
- **スタイル**: 見やすいレイアウト

## カスタマイズ

### フォントの変更

`.vscode/markdown-pdf.css` ファイルの以下の部分を編集：

```css
body {
    font-family: 'Segoe UI', 'Yu Gothic', 'Meiryo', sans-serif;
    /* お好みのフォントに変更 */
}
```

### 余白の調整

`.vscode/settings.json` ファイルの以下の部分を編集：

```json
"markdown-pdf.margin.top": "1.5cm",
"markdown-pdf.margin.bottom": "1cm",
"markdown-pdf.margin.right": "1cm",
"markdown-pdf.margin.left": "1cm"
```

### 色の変更

`.vscode/markdown-pdf.css` ファイルで色を調整：

```css
h1 {
    color: #2c3e50;  /* ヘッダー1の色 */
    border-bottom: 3px solid #3498db;  /* 下線の色 */
}
```

## トラブルシューティング

### PDFが生成されない場合

1. Markdown PDF拡張機能が正しくインストールされているか確認
2. VS Codeを再起動
3. README.mdファイルが正しく開かれているか確認

### 日本語フォントが正しく表示されない場合

1. システムに日本語フォントがインストールされているか確認
2. `.vscode/markdown-pdf.css` のフォント設定を確認

### レイアウトが崩れる場合

1. `.vscode/markdown-pdf.css` の設定を確認
2. 必要に応じてスタイルを調整

## 出力ファイル

- **ファイル名**: `README.pdf`
- **保存場所**: README.mdと同じディレクトリ
- **ファイルサイズ**: 通常数百KB程度

## 注意事項

- PDF出力時は、Markdownの一部の機能（動的コンテンツなど）は反映されません
- 大きな画像やテーブルがある場合、ページ区切りが適切に行われるよう調整が必要な場合があります
- 出力されたPDFは上書きされるため、必要に応じてバックアップを取ってください
