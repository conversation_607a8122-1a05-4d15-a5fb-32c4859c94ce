import pandas as pd
import openpyxl
from pathlib import Path
from collections import Counter, defaultdict

def extract_code_and_description(cell_value):
    """セル値からコードと説明を分割して抽出"""
    if not cell_value or not isinstance(cell_value, str):
        return None, None
    if " - " in cell_value:
        parts = cell_value.split(" - ", 1)
        code = parts[0].strip()
        description = parts[1].strip()
        return code, description
    return cell_value.strip(), None

def analyze_dtc_frequency():
    """DTCコードの頻度分析を実行"""
    file_path = Path("merged_accumulationtime.xlsx")
    if not file_path.exists():
        print(f"エラー: {file_path} が見つかりません。")
        return

    wb = openpyxl.load_workbook(file_path, data_only=True)
    # スキップ対象のシート名
    skip_sheets = {"DTC_Frequency", "DTC_Matrix", "DTC_matrix_transposed"}
    
    # 全シート名を取得
    all_sheets = set(wb.sheetnames)
    
    # スキップ対象のシートが既に存在するか確認
    existing_skip_sheets = skip_sheets.intersection(all_sheets)
    if existing_skip_sheets:
        print(f"以下のシートは既に存在するため、スキップします: {', '.join(existing_skip_sheets)}")
    
    # スキップ対象のシートを除外
    sheets_to_process = all_sheets - skip_sheets
    
    all_data = []
    unique_dtc_records = set()
    sheet_dtc_presence = defaultdict(set)  # 各シートに存在したDTCコードの記録

    for sheet_name in sheets_to_process:
        sheet = wb[sheet_name]
        print(f"処理中: {sheet_name}")
        sheet_data = []
        sheet_unique_count = 0

        for row in range(2, sheet.max_row + 1):
            dtc_cell = sheet[f"A{row}"].value
            dtc_code, dtc_description = extract_code_and_description(dtc_cell)
            mileage = sheet[f"B{row}"].value
            accumulation_time = sheet[f"C{row}"].value
            uwb_cell = sheet[f"D{row}"].value
            uwb_code, uwb_description = extract_code_and_description(uwb_cell)

            if dtc_code:
                unique_key = (dtc_code, mileage, accumulation_time)
                if unique_key not in unique_dtc_records:
                    unique_dtc_records.add(unique_key)
                    row_data = {
                        'Sheet': sheet_name,
                        'DTC_Code': dtc_code,
                        'DTC_Description': dtc_description,
                        'Mileage': mileage,
                        'AccumulationTime': accumulation_time,
                        'UWB_Code': uwb_code,
                        'UWB_Description': uwb_description
                    }
                    sheet_data.append(row_data)
                    sheet_unique_count += 1
                    sheet_dtc_presence[sheet_name].add(dtc_code)

        all_data.extend(sheet_data)
        # print(f"  ユニークデータ数: {sheet_unique_count}")

    df = pd.DataFrame(all_data)
    dtc_codes = df['DTC_Code'].tolist()
    dtc_counter = Counter(dtc_codes)
    ranking = dtc_counter.most_common()

    # print(f"\n=== DTC頻度ランキング（重複除去後） ===")
    # print(f"総ユニークデータ数: {len(dtc_codes)}")
    # print(f"ユニークDTCコード数: {len(dtc_counter)}")

    ranking_df = pd.DataFrame([
        {
            'Rank': i,
            'DTC_Code': dtc_code,
            'Count': count,
            'DTC_Description': df[df['DTC_Code'] == dtc_code]['DTC_Description'].iloc[0]
        }
        for i, (dtc_code, count) in enumerate(ranking, 1)
    ])
    output_dir = Path('02_DTC_Code_Analysis_Result')
    output_dir.mkdir(exist_ok=True)
    
    ranking_df.to_csv(output_dir / 'DTC_ranking.csv', index=False, encoding='utf-8-sig')
    df.to_csv(output_dir / 'all_extracted_DTC_Code.csv', index=False, encoding='utf-8-sig')

    print(f"\n結果を保存しました:")
    print(f"- DTC_ranking.csv: DTCランキング")
    print(f"- all_extracted_DTC_Code.csv: 全抽出データ（重複除去済み）")

    # ✅ シートごとのDTC存在マトリクスを作成
    all_dtc_codes = sorted(set(dtc_counter.keys()))
    matrix_data = []

    for sheet_name in sheets_to_process:
        row = {'Sheet': sheet_name}
        for dtc in all_dtc_codes:
            row[dtc] = '✓' if dtc in sheet_dtc_presence[sheet_name] else '-'
        matrix_data.append(row)

    # ✅ 横向きのDTCマトリクスを作成
    transposed_matrix_data = []
    # ヘッダー行を作成
    header_row = {'DTC_Code': 'Sheet'}
    for sheet_name in sheets_to_process:
        header_row[sheet_name] = sheet_name
    transposed_matrix_data.append(header_row)

    # 各DTCコードについての行を作成
    for dtc in all_dtc_codes:
        row = {'DTC_Code': dtc}
        dtc_description = df[df['DTC_Code'] == dtc]['DTC_Description'].iloc[0]
        row['DTC_Description'] = dtc_description
        for sheet_name in sheets_to_process:
            row[sheet_name] = '✓' if dtc in sheet_dtc_presence[sheet_name] else '-'
        transposed_matrix_data.append(row)

    # データフレームを作成して保存
    matrix_df = pd.DataFrame(matrix_data)
    transposed_df = pd.DataFrame(transposed_matrix_data)
    
    output_dir = Path('02_DTC_Code_Analysis_Result')
    output_dir.mkdir(exist_ok=True)
    
    matrix_df.to_csv(output_dir / 'DTC_matrix.csv', index=False, encoding='utf-8-sig')
    
    # DTC_matrix_transposed.csvを保存し、2行目を削除
    transposed_csv_path = output_dir / 'DTC_matrix_transposed.csv'
    transposed_df.to_csv(transposed_csv_path, index=False, encoding='utf-8-sig')
    
    # 2行目を削除（ヘッダー行は保持）
    if transposed_csv_path.exists():
        df = pd.read_csv(transposed_csv_path, encoding='utf-8-sig')
        if len(df) > 1:  # 2行目以降が存在する場合のみ削除
            df = df.iloc[1:]  # 2行目以降を保持
            df.to_csv(transposed_csv_path, index=False, encoding='utf-8-sig')
    
    df.to_csv(output_dir / 'all_extracted_DTC_Code.csv', index=False, encoding='utf-8-sig')

    print(f"\n結果を保存しました:")
    print(f"- DTC_matrix.csv: 通常のDTCマトリクス")
    print(f"- DTC_matrix_transposed.csv: 横向きのDTCマトリクス")
    print(f"- all_extracted_DTC_Code.csv: 全抽出データ（重複除去済み）")

    # Excelファイルを読み込み
    wb = openpyxl.load_workbook(file_path)
    
    # 各シートを先頭に作成（既存シートは削除）
    for sheet_name in ['DTC_Frequency', 'DTC_Matrix', 'DTC_matrix_transposed']:
        if sheet_name in wb.sheetnames:
            del wb[sheet_name]
    
    # DTC_Frequencyシートを作成
    dtc_frequency_sheet = wb.create_sheet('DTC_Frequency', 0)
    dtc_counter_df = pd.DataFrame(list(dtc_counter.items()), columns=['DTC_Code', 'Count'])
    dtc_counter_df['DTC_Description'] = dtc_counter_df['DTC_Code'].apply(
        lambda code: df[df['DTC_Code'] == code]['DTC_Description'].iloc[0]
    )
    dtc_counter_df.sort_values('Count', ascending=False, inplace=True)
    
    # データをシートに書き込む（ヘッダー付き）
    for col_idx, header in enumerate(dtc_counter_df.columns, 1):
        dtc_frequency_sheet.cell(row=1, column=col_idx, value=header)
    for r_idx, row in enumerate(dtc_counter_df.itertuples(index=False), 2):
        for c_idx, value in enumerate(row, 1):
            dtc_frequency_sheet.cell(row=r_idx, column=c_idx, value=value)
    
    # DTC_Matrixシートを作成
    dtc_matrix_sheet = wb.create_sheet('DTC_Matrix', 0)
    for col_idx, header in enumerate(matrix_df.columns, 1):
        dtc_matrix_sheet.cell(row=1, column=col_idx, value=header)
    for r_idx, row in enumerate(matrix_df.itertuples(index=False), 2):
        for c_idx, value in enumerate(row, 1):
            dtc_matrix_sheet.cell(row=r_idx, column=c_idx, value=value)
    
    # DTC_matrix_transposedシートを作成
    dtc_matrix_transposed_sheet = wb.create_sheet('DTC_matrix_transposed', 0)
    for col_idx, header in enumerate(transposed_df.columns, 1):
        dtc_matrix_transposed_sheet.cell(row=1, column=col_idx, value=header)
    for r_idx, row in enumerate(transposed_df.itertuples(index=False), 2):
        for c_idx, value in enumerate(row, 1):
            dtc_matrix_transposed_sheet.cell(row=r_idx, column=c_idx, value=value)
    
    # DTC_matrix_transposedシートの2行目を削除
    if dtc_matrix_transposed_sheet.max_row > 1:
        dtc_matrix_transposed_sheet.delete_rows(2)
    
    # Excelファイルを保存
    wb.save(file_path)
    
    # CSVファイルを保存（重複を避けるため、一度のみ実行）
    output_dir = Path('02_DTC_Code_Analysis_Result')
    output_dir.mkdir(exist_ok=True)
    
    # CSVファイルを保存
    dtc_counter_df.to_csv(output_dir / 'DTC_ranking.csv', index=False, encoding='utf-8-sig')
    matrix_df.to_csv(output_dir / 'DTC_matrix.csv', index=False, encoding='utf-8-sig')
    
    # DTC_matrix_transposed.csvを保存し、2行目を削除
    transposed_csv_path = output_dir / 'DTC_matrix_transposed.csv'
    transposed_df.to_csv(transposed_csv_path, index=False, encoding='utf-8-sig')
    
    # 2行目を削除（ヘッダー行は保持）
    if transposed_csv_path.exists():
        df = pd.read_csv(transposed_csv_path, encoding='utf-8-sig')
        if len(df) > 1:  # 2行目以降が存在する場合のみ削除
            df = df.iloc[1:]  # 2行目以降を保持
            df.to_csv(transposed_csv_path, index=False, encoding='utf-8-sig')
    
    df.to_csv(output_dir / 'all_extracted_DTC_Code.csv', index=False, encoding='utf-8-sig')
    
    # 結果の表示（重複を避けるため、一度のみ実行）
    print("\n結果を保存しました：")
    print(f"- DTC_ranking.csv: DTCランキング")
    print(f"- DTC_matrix.csv: 通常のDTCマトリクス")
    print(f"- DTC_matrix_transposed.csv: 横向きのDTCマトリクス")
    print(f"- all_extracted_DTC_Code.csv: 全抽出データ（重複除去済み）")
    print(f"- Excelファイルに3つのシートを追加：DTC_Frequency, DTC_Matrix, DTC_matrix_transposed")

if __name__ == "__main__":
    analyze_dtc_frequency()
