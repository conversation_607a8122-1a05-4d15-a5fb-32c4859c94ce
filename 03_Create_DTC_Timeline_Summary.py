import pandas as pd
import openpyxl
from pathlib import Path
import shutil

def load_uwb_data():
    """03_Input_Filesフォルダ内のUWB_Extraction_List.csvからUWBデータを読み込み"""
    uwb_file = Path("03_Input_Files") / "UWB_Extraction_List.csv"
    if not uwb_file.exists():
        print(f"エラー: {uwb_file} が見つかりません。")
        return None
    df = pd.read_csv(uwb_file)
    return df

def load_dtc_data():
    """03_Input_Filesフォルダ内のDTC_Extraction_List.csvからDTCデータを読み込み"""
    dtc_file = Path("03_Input_Files") / "DTC_Extraction_List.csv"
    if not dtc_file.exists():
        print(f"エラー: {dtc_file} が見つかりません。")
        return None
    
    df = pd.read_csv(dtc_file)
    return set(df['DTC_Code'].tolist())

def extract_code_and_description(text):
    """"-"でコードと説明を分割"""
    if pd.isna(text) or not isinstance(text, str):
        return None, None
    
    parts = text.split(" - ", 1)
    code = parts[0].strip() if len(parts) > 0 else None
    description = parts[1].strip() if len(parts) > 1 else None
    return code, description

def extract_numeric_value(value, unit):
    """値から単位を除去して数値を抽出"""
    if value is None:
        return 0
    
    value_str = str(value).strip()
    if unit in value_str:
        value_str = value_str.replace(unit, '').strip()
    
    try:
        return float(value_str)
    except ValueError:
        return 0

def process_sheet_data(sheet, target_dtc_codes):
    """各シートからデータを抽出"""
    data = []
    
    # まず全ての行のデータを収集
    all_rows = []
    for row in range(2, sheet.max_row + 1):  # 1行目はヘッダー
        dtc_cell = sheet[f"A{row}"].value
        mileage_cell = sheet[f"B{row}"].value
        accumulation_cell = sheet[f"C{row}"].value
        uwb_cell = sheet[f"D{row}"].value
        e_cell = sheet[f"E{row}"].value
        
        if dtc_cell:
            dtc_code, dtc_description = extract_code_and_description(dtc_cell)
            uwb_code, uwb_description = extract_code_and_description(uwb_cell)
            
            all_rows.append({
                'DTC_Code': dtc_code,
                'DTC_Description': dtc_description,
                'Mileage': mileage_cell,
                'AccumulationTime': accumulation_cell,
                'UWB_Code': uwb_code,
                'UWB_Description': uwb_description,
                'E_Value': e_cell
            })
    
    # 対象DTCコードのAccumulationTimeを基準に±300秒内のデータを抽出
    target_times = []
    for row_data in all_rows:
        if row_data['DTC_Code'] in target_dtc_codes:
            target_times.append(row_data['AccumulationTime'])
    
    # ±300秒内のデータを抽出
    for row_data in all_rows:
        if not target_times:  # 対象DTCコードが見つからない場合はスキップ
            continue
            
        # AccumulationTimeを数値に変換
        try:
            row_time = float(str(row_data['AccumulationTime']).replace('s', '').strip())
            
            # ±300秒内のデータを追加
            if any(abs(row_time - float(str(target_time).replace('s', '').strip())) <= 300 for target_time in target_times):
                data.append(row_data)
        except (ValueError, TypeError):
            continue
    
    return data

def create_timeline_summary():
    """DTC Timeline Summaryを作成"""
    # 1. merged_accumulationtime.xlsxをコピー
    source_file = Path("merged_accumulationtime.xlsx")
    target_file = Path("merged_accumulationtime_with_timeline.xlsx")
    
    if not source_file.exists():
        print(f"エラー: {source_file} が見つかりません。")
        return
    
    shutil.copy2(source_file, target_file)
    print(f"{source_file} を {target_file} にコピーしました。")
    
    # 2. 必要なデータを読み込み
    uwb_df = load_uwb_data()
    target_dtc_codes = load_dtc_data()
    
    if uwb_df is None or not target_dtc_codes:
        return
    
    # 3. コピーしたファイルを開く
    wb = openpyxl.load_workbook(target_file)
    
    # 4. DTC_Timeline_Summaryシートを作成（既存の場合は削除）
    if "DTC_Timeline_Summary" in wb.sheetnames:
        wb.remove(wb["DTC_Timeline_Summary"])
    
    summary_sheet = wb.create_sheet("DTC_Timeline_Summary", 0)
    
    # 5. UWBデータの準備
    uwb_categories = uwb_df['Categoy'].tolist()
    uwb_codes = uwb_df['UWB_Code'].tolist()
    uwb_descriptions = uwb_df['UWB_Description'].tolist()
    
    # 6. ヘッダー行を作成
    summary_sheet.append(['', '', '', ''] + uwb_categories)
    summary_sheet.append(['', '', '', ''] + uwb_codes)
    summary_sheet.append(['', '', '', ''] + uwb_descriptions)
    
    # 7. 全シートのデータを収集
    all_sheet_data = {}  # シート名をキーとしてデータを保存
    
    for sheet_name in wb.sheetnames:
        if sheet_name in ["DTC_Timeline_Summary", "DTC_Frequency", "DTC_Matrix", "DTC_matrix_transposed"]:
            continue
            
        sheet = wb[sheet_name]
        sheet_data = process_sheet_data(sheet, target_dtc_codes)
        all_sheet_data[sheet_name] = sheet_data
    
    # 8-9. 各シート毎にデータを書き込み
    for sheet_name, sheet_data in all_sheet_data.items():
        if not sheet_data:  # データがない場合はスキップ
            continue
            
        # シート名を記載（4行目）
        summary_sheet.append([sheet_name, '', '', ''] + [''] * len(uwb_codes))
        
        # ヘッダー行を記載（5行目）
        summary_sheet.append(['DTCコード', 'DTC_Description', 'Mileage', 'AccumulationTime'] + ['Val'] * len(uwb_codes))
        
        # DTCコード毎にユニークなデータを取得
        unique_dtc_data = {}
        for data in sheet_data:
            # DTCコード + Mileage + AccumulationTimeをキーにする
            dtc_key = (data['DTC_Code'], data['Mileage'], data['AccumulationTime'])
            if dtc_key not in unique_dtc_data:
                unique_dtc_data[dtc_key] = {
                    'DTC_Code': data['DTC_Code'],
                    'DTC_Description': data['DTC_Description'],
                    'Mileage': data['Mileage'],
                    'AccumulationTime': data['AccumulationTime'],
                    'uwb_data': {}
                }
            # UWBデータを追加
            if data['UWB_Code']:
                unique_dtc_data[dtc_key]['uwb_data'][data['UWB_Code']] = data['E_Value']
        
        # MileageとAccumulationTimeでソート
        sorted_dtc_data = sorted(unique_dtc_data.values(), 
                               key=lambda x: (
                                   extract_numeric_value(x['Mileage'], 'km'),
                                   extract_numeric_value(x['AccumulationTime'], 's')
                               ))
        
        # DTCコード毎にデータ行を追加（6行目以降）
        for dtc_data in sorted_dtc_data:
            # UWBコードとの照合
            uwb_values = []
            for uwb_code in uwb_codes:
                if uwb_code in dtc_data['uwb_data']:
                    found_value = dtc_data['uwb_data'][uwb_code]
                    uwb_values.append(found_value if found_value is not None else "-")
                else:
                    uwb_values.append("-")
            
            # データ行を追加
            summary_sheet.append([
                dtc_data['DTC_Code'],
                dtc_data['DTC_Description'],
                dtc_data['Mileage'],
                dtc_data['AccumulationTime']
            ] + uwb_values)
    
    # 10. ファイルを保存
    wb.save(target_file)
    print(f"DTC_Timeline_Summary シートを {target_file} に作成しました。")

if __name__ == "__main__":
    create_timeline_summary()
